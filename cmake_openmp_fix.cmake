# CMakeLists.txt中OpenMP配置的修复方案

# 设置最低CMake版本
cmake_minimum_required(VERSION 3.10)

# 查找CUDA
find_package(CUDA REQUIRED)

# 查找OpenMP - 只查找C和C++组件
find_package(OpenMP)

if(OpenMP_FOUND)
    message(STATUS "OpenMP found")
    message(STATUS "OpenMP_C_FLAGS: ${OpenMP_C_FLAGS}")
    message(STATUS "OpenMP_CXX_FLAGS: ${OpenMP_CXX_FLAGS}")
    
    # 设置编译标志
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
    
    # 如果使用现代CMake目标
    if(TARGET OpenMP::OpenMP_C)
        target_link_libraries(your_target_name OpenMP::OpenMP_C)
    endif()
    
    if(TARGET OpenMP::OpenMP_CXX)
        target_link_libraries(your_target_name OpenMP::OpenMP_CXX)
    endif()
else()
    message(WARNING "OpenMP not found")
endif()

# 对于CUDA代码，手动设置OpenMP标志
if(CUDA_FOUND)
    # 为CUDA编译器添加OpenMP支持
    list(APPEND CUDA_NVCC_FLAGS "-Xcompiler -fopenmp")
    
    # 设置CUDA架构
    set(CUDA_NVCC_FLAGS ${CUDA_NVCC_FLAGS} "-gencode arch=compute_70,code=sm_70")
    set(CUDA_NVCC_FLAGS ${CUDA_NVCC_FLAGS} "-gencode arch=compute_75,code=sm_75")
    set(CUDA_NVCC_FLAGS ${CUDA_NVCC_FLAGS} "-gencode arch=compute_80,code=sm_80")
endif()

# 如果仍然遇到OpenMP_CUDA问题，可以手动设置
if(NOT OpenMP_CUDA_FOUND)
    set(OpenMP_CUDA_FOUND FALSE CACHE BOOL "Disable CUDA OpenMP" FORCE)
endif()
