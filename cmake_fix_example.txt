# 在find_package(OpenMP)之前添加以下行来禁用CUDA OpenMP支持

# 方法1：完全禁用CUDA OpenMP
set(OpenMP_CUDA_FOUND FALSE)

# 方法2：只查找C和C++的OpenMP支持
find_package(OpenMP REQUIRED COMPONENTS C CXX)

# 方法3：设置OpenMP组件（如果CMake版本支持）
find_package(OpenMP REQUIRED)
if(OpenMP_FOUND)
    # 只使用C和C++的OpenMP
    set(OpenMP_TARGETS OpenMP::OpenMP_C OpenMP::OpenMP_CXX)
endif()

# 原始的find_package调用应该替换为上述方法之一
# find_package(OpenMP REQUIRED)  # 替换这行
