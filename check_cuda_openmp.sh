#!/bin/bash

echo "检查CUDA和OpenMP支持..."

# 检查CUDA版本
echo "=== CUDA版本 ==="
nvcc --version

# 检查OpenMP支持
echo "=== OpenMP支持检查 ==="
echo "检查C编译器OpenMP支持："
gcc -fopenmp --version

echo "检查C++编译器OpenMP支持："
g++ -fopenmp --version

# 检查CUDA编译器是否支持OpenMP
echo "=== CUDA编译器OpenMP支持 ==="
nvcc -Xcompiler -fopenmp --version

# 检查系统中的OpenMP库
echo "=== 系统OpenMP库 ==="
find /usr -name "*omp*" -type f 2>/dev/null | head -10

echo "=== CMake版本 ==="
cmake --version
